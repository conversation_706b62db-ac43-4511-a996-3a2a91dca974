<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebSocket客户端示例</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stomp-websocket@2.3.4-next/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        #messages {
            border: 1px solid #ddd;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 3px;
            background-color: white;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🔗 简单WebSocket客户端</h1>
    
    <div id="status" class="status disconnected">❌ 未连接</div>
    
    <div>
        <button id="connectBtn" class="btn-primary" onclick="connect()">连接</button>
        <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>断开</button>
        <button onclick="clearMessages()">清空消息</button>
    </div>
    
    <h3>📨 接收到的消息:</h3>
    <div id="messages"></div>
    
    <script src="mqtt-websocket-client.js"></script>
    <script>
        let client = null;
        
        function connect() {
            // 创建客户端实例
            client = new MqttWebSocketClient('http://localhost:8081/ws');
            
            // 设置事件回调
            client.onMessage = function(messageData) {
                addMessage(`[${formatTime(messageData.timestamp)}] #${messageData.count}: ${messageData.content}`);
            };
            
            client.onConnect = function() {
                updateStatus('connected', '✅ 已连接');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                addMessage('🎉 连接成功，等待MQTT消息...');
            };
            
            client.onDisconnect = function() {
                updateStatus('disconnected', '❌ 已断开连接');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                addMessage('👋 连接已断开');
            };
            
            client.onError = function(error) {
                updateStatus('disconnected', '❌ 连接失败: ' + error);
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                addMessage('❌ 错误: ' + error);
            };
            
            client.onStatusChange = function(statusData) {
                if (statusData.status === 'connecting') {
                    updateStatus('connecting', '🔄 正在连接...');
                }
            };
            
            // 开始连接
            client.connect();
        }
        
        function disconnect() {
            if (client) {
                client.disconnect();
            }
        }
        
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + status;
            statusDiv.textContent = message;
        }
        
        function addMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        function formatTime(date) {
            return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            addMessage('💡 点击"连接"按钮开始接收MQTT消息');
        };
        
        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (client) {
                client.disconnect();
            }
        };
    </script>
</body>
</html>
