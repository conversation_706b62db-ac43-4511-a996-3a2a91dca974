# File to MQTT 功能说明

## 功能概述

这个功能可以从项目根目录的指定文件中按行读取数据，并以指定的时间间隔发送到MQTT broker。

**🔄 循环读取模式：读完文件后会自动从第一行重新开始读取，实现无限循环发送**

**默认使用项目根目录的 `result.txt` 文件**

## 主要组件

### 1. FileToMqttService
核心服务类，负责文件读取和MQTT消息发送。

**主要方法：**
- `startSendingFromFile(String filePath, long intervalMs)` - 使用默认主题发送
- `startSendingFromFile(String filePath, long intervalMs, String topic)` - 使用指定主题发送
- `stop()` - 停止发送服务
- `isRunning()` - 检查服务状态

### 2. MqttPublishService
MQTT消息发布服务。

### 3. FileToMqttController
REST API控制器，提供HTTP接口控制文件发送服务。

## 使用方法

### 1. 通过代码调用

```java
@Autowired
private FileToMqttService fileToMqttService;

// 从项目根目录的result.txt文件每50ms读取一行发送到默认主题
fileToMqttService.startSendingFromFile("result.txt", 50);

// 从项目根目录的data.txt文件每100ms读取一行发送到指定主题
fileToMqttService.startSendingFromFile("data.txt", 100, "sensor/temperature");

// 停止服务
fileToMqttService.stop();
```

### 2. 通过REST API

#### 启动发送服务
```bash
POST /api/file-to-mqtt/start
Content-Type: application/json

{
    "filePath": "result.txt",
    "intervalMs": 50,
    "topic": "sensor/data"
}
```

#### 停止发送服务
```bash
POST /api/file-to-mqtt/stop
```

#### 查看服务状态
```bash
GET /api/file-to-mqtt/status
```

### 3. 运行测试

#### 单元测试
```bash
mvn test -Dtest=FileToMqttServiceTest
```

#### 集成测试
```bash
mvn test -Dtest=FileToMqttIntegrationTest
```

#### 演示程序
```bash
mvn test -Dtest=FileToMqttDemo
```

## 配置说明

在 `application.properties` 中配置MQTT连接：

```properties
# MQTT Configuration
mqtt.broker.url=tcp://localhost:1883
mqtt.client.id=mqttWebsocketBridge
mqtt.topic=sensor/data
```

## 文件格式

支持任何文本文件，每行作为一条MQTT消息发送。示例：

```
{"timestamp": "2024-01-01T10:00:00Z", "sensor": "temperature", "value": 23.5}
{"timestamp": "2024-01-01T10:00:01Z", "sensor": "humidity", "value": 65.2}
{"timestamp": "2024-01-01T10:00:02Z", "sensor": "pressure", "value": 1013.25}
```

## 特性

- ✅ **循环读取模式** - 文件读取完毕后自动从第一行重新开始
- ✅ 支持自定义发送间隔（毫秒级）
- ✅ 支持自定义MQTT主题
- ✅ 自动跳过空行
- ✅ 线程安全
- ✅ 可以随时启动/停止
- ✅ 防止重复启动
- ✅ 智能内存管理（一次性加载所有行）
- ✅ 循环计数和日志记录
- ✅ 异常处理和详细日志

## 监控MQTT消息

### 1. 使用MQTT客户端
```bash
# 使用mosquitto客户端订阅消息
mosquitto_sub -h localhost -t "sensor/data"
```

### 2. 查看WebSocket页面
访问：http://localhost:8080

### 3. 查看日志
服务会输出详细的日志信息，包括发送的消息内容。

## 注意事项

1. 确保MQTT broker正在运行
2. 确保文件路径正确且文件存在
3. 服务是单线程的，同时只能处理一个文件
4. 大文件处理时注意内存使用
5. 发送间隔不要设置得太小，避免过载MQTT broker

## 示例用法

```java
// 示例：从result.txt每50ms发送一行数据
@Component
public class DataSender {

    @Autowired
    private FileToMqttService fileToMqttService;

    public void startSendingData() {
        // 开始发送
        fileToMqttService.startSendingFromFile("result.txt", 50);

        // 5秒后停止
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                fileToMqttService.stop();
            }
        }, 5000);
    }
}
```
