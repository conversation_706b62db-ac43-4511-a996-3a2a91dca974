<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT WebSocket 客户端</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stomp-websocket@2.3.4-next/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-connect {
            background-color: #28a745;
            color: white;
        }
        .btn-disconnect {
            background-color: #dc3545;
            color: white;
        }
        .btn-clear {
            background-color: #6c757d;
            color: white;
        }
        .message-area {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .message-time {
            color: #6c757d;
            font-size: 11px;
        }
        .message-content {
            margin-top: 3px;
            word-break: break-all;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 MQTT WebSocket 实时客户端</h1>
        
        <!-- 连接状态 -->
        <div id="status" class="status disconnected">
            ❌ 未连接
        </div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button id="connectBtn" class="btn-connect" onclick="connect()">连接 WebSocket</button>
            <button id="disconnectBtn" class="btn-disconnect" onclick="disconnect()" disabled>断开连接</button>
            <button class="btn-clear" onclick="clearMessages()">清空消息</button>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-item">
                <div id="messageCount" class="stat-value">0</div>
                <div class="stat-label">接收消息数</div>
            </div>
            <div class="stat-item">
                <div id="connectionTime" class="stat-value">--</div>
                <div class="stat-label">连接时长</div>
            </div>
            <div class="stat-item">
                <div id="messageRate" class="stat-value">0</div>
                <div class="stat-label">消息/秒</div>
            </div>
        </div>
        
        <!-- 消息显示区域 -->
        <h3>📨 实时消息 (最新消息在顶部)</h3>
        <div id="messageArea" class="message-area">
            <div style="text-align: center; color: #6c757d; margin-top: 180px;">
                等待连接和消息...
            </div>
        </div>
    </div>

    <script>
        let stompClient = null;
        let messageCount = 0;
        let connectTime = null;
        let connectionTimer = null;
        let rateTimer = null;
        let lastMessageTime = Date.now();
        let messageRateCounter = 0;

        // 连接WebSocket
        function connect() {
            const serverUrl = 'http://localhost:8081/ws';
            
            updateStatus('connecting', '🔄 正在连接...');
            document.getElementById('connectBtn').disabled = true;
            
            try {
                const socket = new SockJS(serverUrl);
                stompClient = Stomp.over(socket);
                
                // 设置调试信息（可选）
                stompClient.debug = function(str) {
                    console.log('STOMP Debug:', str);
                };
                
                stompClient.connect({}, 
                    function(frame) {
                        // 连接成功
                        console.log('WebSocket连接成功:', frame);
                        updateStatus('connected', '✅ 已连接到 ' + serverUrl);
                        document.getElementById('connectBtn').disabled = true;
                        document.getElementById('disconnectBtn').disabled = false;
                        
                        // 记录连接时间
                        connectTime = Date.now();
                        startConnectionTimer();
                        startRateTimer();
                        
                        // 订阅消息主题
                        stompClient.subscribe('/topic/messages', function(message) {
                            showMessage(message.body);
                        });
                        
                        addSystemMessage('🎉 成功连接到WebSocket服务器');
                        addSystemMessage('📡 已订阅主题: /topic/messages');
                        addSystemMessage('⏳ 等待MQTT消息转发...');
                    },
                    function(error) {
                        // 连接失败
                        console.error('WebSocket连接失败:', error);
                        updateStatus('disconnected', '❌ 连接失败: ' + error);
                        document.getElementById('connectBtn').disabled = false;
                        document.getElementById('disconnectBtn').disabled = true;
                        addSystemMessage('❌ 连接失败: ' + error, 'error');
                    }
                );
            } catch (error) {
                console.error('创建WebSocket连接时出错:', error);
                updateStatus('disconnected', '❌ 连接错误: ' + error.message);
                document.getElementById('connectBtn').disabled = false;
                addSystemMessage('❌ 连接错误: ' + error.message, 'error');
            }
        }

        // 断开连接
        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect(function() {
                    console.log('WebSocket已断开连接');
                    updateStatus('disconnected', '❌ 已断开连接');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    stopTimers();
                    addSystemMessage('👋 已断开WebSocket连接');
                });
            }
        }

        // 显示接收到的消息
        function showMessage(messageBody) {
            messageCount++;
            messageRateCounter++;
            lastMessageTime = Date.now();
            
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
            
            messageDiv.innerHTML = `
                <div class="message-time">${timeStr} - 消息 #${messageCount}</div>
                <div class="message-content">${escapeHtml(messageBody)}</div>
            `;
            
            // 将新消息插入到顶部
            messageArea.insertBefore(messageDiv, messageArea.firstChild);
            
            // 限制显示的消息数量（保留最新的1000条）
            while (messageArea.children.length > 1000) {
                messageArea.removeChild(messageArea.lastChild);
            }
            
            // 更新统计
            document.getElementById('messageCount').textContent = messageCount;
        }

        // 添加系统消息
        function addSystemMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.style.borderLeftColor = type === 'error' ? '#dc3545' : '#28a745';
            messageDiv.style.backgroundColor = type === 'error' ? '#f8d7da' : '#d4edda';
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
            
            messageDiv.innerHTML = `
                <div class="message-time">${timeStr} - 系统消息</div>
                <div class="message-content">${escapeHtml(message)}</div>
            `;
            
            messageArea.insertBefore(messageDiv, messageArea.firstChild);
        }

        // 更新连接状态
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + status;
            statusDiv.textContent = message;
        }

        // 清空消息
        function clearMessages() {
            document.getElementById('messageArea').innerHTML = `
                <div style="text-align: center; color: #6c757d; margin-top: 180px;">
                    消息已清空，等待新消息...
                </div>
            `;
            messageCount = 0;
            messageRateCounter = 0;
            document.getElementById('messageCount').textContent = '0';
            document.getElementById('messageRate').textContent = '0';
        }

        // 开始连接时间计时器
        function startConnectionTimer() {
            connectionTimer = setInterval(function() {
                if (connectTime) {
                    const duration = Math.floor((Date.now() - connectTime) / 1000);
                    const minutes = Math.floor(duration / 60);
                    const seconds = duration % 60;
                    document.getElementById('connectionTime').textContent = 
                        minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
                }
            }, 1000);
        }

        // 开始消息速率计时器
        function startRateTimer() {
            rateTimer = setInterval(function() {
                document.getElementById('messageRate').textContent = messageRateCounter;
                messageRateCounter = 0;
            }, 1000);
        }

        // 停止所有计时器
        function stopTimers() {
            if (connectionTimer) {
                clearInterval(connectionTimer);
                connectionTimer = null;
            }
            if (rateTimer) {
                clearInterval(rateTimer);
                rateTimer = null;
            }
            connectTime = null;
            document.getElementById('connectionTime').textContent = '--';
            document.getElementById('messageRate').textContent = '0';
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            addSystemMessage('🚀 WebSocket客户端已加载');
            addSystemMessage('💡 点击"连接 WebSocket"按钮开始接收MQTT消息');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
        };
    </script>
</body>
</html>
