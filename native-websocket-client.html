<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原生WebSocket客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        #messages {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 3px;
            background-color: white;
            border-left: 3px solid #007bff;
        }
        .system-message {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error-message {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .debug-message {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            font-size: 10px;
        }
        .controls {
            margin: 10px 0;
        }
        input[type="text"] {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>🔗 原生WebSocket客户端 (STOMP协议)</h1>
    
    <div class="controls">
        <label>WebSocket URL:</label>
        <input type="text" id="wsUrl" value="ws://localhost:8081/ws/websocket" />
    </div>
    
    <div id="status" class="status disconnected">❌ 未连接</div>
    
    <div class="controls">
        <button id="connectBtn" class="btn-primary" onclick="connect()">连接</button>
        <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>断开</button>
        <button class="btn-secondary" onclick="clearMessages()">清空消息</button>
        <button class="btn-secondary" onclick="toggleDebug()">切换调试模式</button>
    </div>
    
    <h3>📨 消息日志:</h3>
    <div id="messages"></div>
    
    <script>
        let ws = null;
        let isConnected = false;
        let messageCount = 0;
        let debugMode = false;
        
        // STOMP协议常量
        const STOMP_COMMANDS = {
            CONNECT: 'CONNECT',
            CONNECTED: 'CONNECTED',
            SUBSCRIBE: 'SUBSCRIBE',
            MESSAGE: 'MESSAGE',
            DISCONNECT: 'DISCONNECT',
            ERROR: 'ERROR'
        };
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            if (isConnected) {
                addMessage('⚠️ 已经连接，请先断开', 'system');
                return;
            }
            
            updateStatus('connecting', '🔄 正在连接...');
            addMessage(`🚀 尝试连接到: ${url}`, 'system');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    addMessage('✅ WebSocket连接已建立', 'system');
                    sendStompConnect();
                };
                
                ws.onmessage = function(event) {
                    handleStompMessage(event.data);
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('disconnected', '❌ 连接已关闭');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    addMessage(`👋 连接已关闭 (代码: ${event.code}, 原因: ${event.reason})`, 'system');
                };
                
                ws.onerror = function(error) {
                    addMessage('❌ WebSocket错误: ' + error, 'error');
                    updateStatus('disconnected', '❌ 连接错误');
                };
                
            } catch (error) {
                addMessage('❌ 创建WebSocket连接失败: ' + error.message, 'error');
                updateStatus('disconnected', '❌ 连接失败');
            }
        }
        
        function disconnect() {
            if (ws && isConnected) {
                sendStompDisconnect();
                ws.close();
            }
        }
        
        // 发送STOMP CONNECT帧
        function sendStompConnect() {
            const connectFrame = [
                'CONNECT',
                'accept-version:1.0,1.1,2.0',
                'heart-beat:10000,10000',
                '',
                ''
            ].join('\n') + '\0';
            
            ws.send(connectFrame);
            addMessage('📤 发送STOMP CONNECT帧', 'debug');
        }
        
        // 发送STOMP SUBSCRIBE帧
        function sendStompSubscribe() {
            const subscribeFrame = [
                'SUBSCRIBE',
                'id:sub-0',
                'destination:/topic/messages',
                '',
                ''
            ].join('\n') + '\0';
            
            ws.send(subscribeFrame);
            addMessage('📤 发送STOMP SUBSCRIBE帧 (主题: /topic/messages)', 'debug');
        }
        
        // 发送STOMP DISCONNECT帧
        function sendStompDisconnect() {
            const disconnectFrame = [
                'DISCONNECT',
                '',
                ''
            ].join('\n') + '\0';
            
            ws.send(disconnectFrame);
            addMessage('📤 发送STOMP DISCONNECT帧', 'debug');
        }
        
        // 处理STOMP消息
        function handleStompMessage(data) {
            if (debugMode) {
                addMessage('📥 原始STOMP帧: ' + data.replace(/\0/g, '\\0'), 'debug');
            }
            
            // 解析STOMP帧
            const frame = parseStompFrame(data);
            
            switch (frame.command) {
                case STOMP_COMMANDS.CONNECTED:
                    handleStompConnected(frame);
                    break;
                case STOMP_COMMANDS.MESSAGE:
                    handleStompMessage_Content(frame);
                    break;
                case STOMP_COMMANDS.ERROR:
                    handleStompError(frame);
                    break;
                default:
                    if (debugMode) {
                        addMessage('📥 未知STOMP命令: ' + frame.command, 'debug');
                    }
            }
        }
        
        // 解析STOMP帧
        function parseStompFrame(data) {
            const lines = data.split('\n');
            const command = lines[0];
            const headers = {};
            let bodyStart = 1;
            
            // 解析头部
            for (let i = 1; i < lines.length; i++) {
                if (lines[i] === '') {
                    bodyStart = i + 1;
                    break;
                }
                const colonIndex = lines[i].indexOf(':');
                if (colonIndex > 0) {
                    const key = lines[i].substring(0, colonIndex);
                    const value = lines[i].substring(colonIndex + 1);
                    headers[key] = value;
                }
            }
            
            // 解析消息体
            const body = lines.slice(bodyStart).join('\n').replace(/\0$/, '');
            
            return {
                command: command,
                headers: headers,
                body: body
            };
        }
        
        // 处理STOMP CONNECTED
        function handleStompConnected(frame) {
            isConnected = true;
            updateStatus('connected', '✅ STOMP连接成功');
            document.getElementById('connectBtn').disabled = true;
            document.getElementById('disconnectBtn').disabled = false;
            
            addMessage('🎉 STOMP连接成功！', 'system');
            
            // 订阅消息主题
            sendStompSubscribe();
            addMessage('📡 已订阅MQTT消息主题: /topic/messages', 'system');
        }
        
        // 处理STOMP MESSAGE (MQTT消息)
        function handleStompMessage_Content(frame) {
            messageCount++;
            const timestamp = new Date().toLocaleTimeString() + '.' + 
                            new Date().getMilliseconds().toString().padStart(3, '0');
            
            addMessage(`[${timestamp}] MQTT消息 #${messageCount}: ${frame.body}`, 'message');
            
            if (debugMode) {
                addMessage(`📋 消息头部: ${JSON.stringify(frame.headers)}`, 'debug');
            }
        }
        
        // 处理STOMP ERROR
        function handleStompError(frame) {
            addMessage('❌ STOMP错误: ' + frame.body, 'error');
            if (frame.headers.message) {
                addMessage('❌ 错误详情: ' + frame.headers.message, 'error');
            }
        }
        
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + status;
            statusDiv.textContent = message;
        }
        
        function addMessage(message, type = 'message') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            
            let className = 'message';
            if (type === 'system') className += ' system-message';
            else if (type === 'error') className += ' error-message';
            else if (type === 'debug') className += ' debug-message';
            
            messageDiv.className = className;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
        }
        
        function toggleDebug() {
            debugMode = !debugMode;
            addMessage(`🔧 调试模式: ${debugMode ? '开启' : '关闭'}`, 'system');
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            addMessage('💡 这是一个使用原生WebSocket + STOMP协议的客户端', 'system');
            addMessage('🔗 点击"连接"按钮开始接收MQTT消息', 'system');
            addMessage('⚙️ 可以点击"切换调试模式"查看详细的STOMP协议交互', 'system');
        };
        
        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (ws && isConnected) {
                disconnect();
            }
        };
    </script>
</body>
</html>
