# 使用示例：从项目根目录的result.txt读取数据发送到MQTT

## 快速开始

### 1. 确保result.txt文件存在
项目会自动从项目根目录读取 `result.txt` 文件：
```
C:/Users/<USER>/Documents/work/code/java/ChangLiRoad/result.txt
```

### 2. 通过代码调用

```java
@Autowired
private FileToMqttService fileToMqttService;

// 从项目根目录的result.txt每50ms读取一行发送到默认主题
fileToMqttService.startSendingFromFile("result.txt", 50);

// 检查服务状态
if (fileToMqttService.isRunning()) {
    System.out.println("服务正在运行...");
}

// 停止服务
fileToMqttService.stop();
```

### 3. 通过REST API调用

#### 启动服务（使用默认配置）
```bash
curl -X POST http://localhost:8080/api/file-to-mqtt/start \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 启动服务（自定义配置）
```bash
curl -X POST http://localhost:8080/api/file-to-mqtt/start \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "result.txt",
    "intervalMs": 50,
    "topic": "sensor/data"
  }'
```

#### 停止服务
```bash
curl -X POST http://localhost:8080/api/file-to-mqtt/stop
```

#### 查看服务状态
```bash
curl http://localhost:8080/api/file-to-mqtt/status
```

### 4. 运行演示测试

```bash
# 运行演示测试（会自动使用项目根目录的result.txt）
mvn test -Dtest=ManualFileToMqttTest
```

## 演示结果

刚才的测试显示：
- ✅ 成功读取项目根目录的result.txt文件（2.5MB）
- ✅ 文件路径：`C:\Users\<USER>\Documents\work\code\java\ChangLiRoad\result.txt`
- ✅ 每50ms发送一行数据到MQTT
- ✅ 3秒内发送了49条消息
- ✅ 服务可以正常启动和停止

## 配置说明

### 默认配置
- **文件路径**: `result.txt`（项目根目录）
- **发送间隔**: 50ms
- **MQTT主题**: `sensor/data`（默认）
- **MQTT Broker**: `tcp://localhost:1883`

### 自定义配置
你可以通过REST API或代码调用来自定义：
- 文件路径（相对于项目根目录）
- 发送间隔（毫秒）
- MQTT主题

## 注意事项

1. **文件路径**: 所有文件路径都是相对于项目根目录的
2. **文件格式**: 支持任何文本文件，每行作为一条MQTT消息
3. **服务状态**: 同时只能运行一个文件读取服务
4. **自动停止**: 文件读取完毕后服务会自动停止
5. **错误处理**: 如果文件不存在或读取出错，服务会自动停止并记录错误

## 监控MQTT消息

### 使用MQTT客户端
```bash
# 订阅默认主题
mosquitto_sub -h localhost -t "sensor/data"

# 订阅自定义主题
mosquitto_sub -h localhost -t "your/custom/topic"
```

### 使用WebSocket页面
访问：http://localhost:8080

### 查看日志
服务会输出详细的日志信息，包括：
- 服务启动/停止状态
- 发送的消息内容（DEBUG级别）
- 错误信息
