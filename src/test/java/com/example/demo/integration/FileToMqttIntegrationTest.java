package com.example.demo.integration;

import com.example.demo.service.FileToMqttService;
import com.example.demo.service.MqttPublishService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@SpringBootTest
@TestPropertySource(properties = {
    "mqtt.broker.url=tcp://localhost:1883",
    "mqtt.client.id=testClient",
    "mqtt.topic=test/topic"
})
class FileToMqttIntegrationTest {

    @Autowired
    private FileToMqttService fileToMqttService;

    @MockBean
    private MqttPublishService mqttPublishService;

    @Test
    void testReadFromResultTxtAndSendToMqtt() throws InterruptedException, IOException {
        // 检查result.txt文件是否存在
        Path resultFile = Paths.get("result.txt");
        assertTrue(Files.exists(resultFile), "result.txt file should exist in project root");

        // 启动文件读取服务，每50ms读取一行
        fileToMqttService.startSendingFromFile("result.txt", 50);

        // 验证服务已启动
        assertTrue(fileToMqttService.isRunning(), "Service should be running");

        // 等待一段时间让服务处理一些数据
        Thread.sleep(500); // 等待500ms，应该能处理约10行数据

        // 停止服务
        fileToMqttService.stop();

        // 验证服务已停止
        assertFalse(fileToMqttService.isRunning(), "Service should be stopped");

        // 验证至少发送了一些消息
        verify(mqttPublishService, atLeast(1)).publishMessage(anyString());
    }

    @Test
    void testReadFromResultTxtWithCustomTopic() throws InterruptedException {
        String customTopic = "sensor/data/test";

        // 启动文件读取服务，使用自定义主题
        fileToMqttService.startSendingFromFile("result.txt", 50, customTopic);

        // 验证服务已启动
        assertTrue(fileToMqttService.isRunning());

        // 等待一段时间
        Thread.sleep(300);

        // 停止服务
        fileToMqttService.stop();

        // 验证发送到了自定义主题
        verify(mqttPublishService, atLeast(1)).publishMessage(eq(customTopic), anyString());
    }

    @Test
    void testServiceStatusCheck() {
        // 初始状态应该是未运行
        assertFalse(fileToMqttService.isRunning());

        // 启动服务
        fileToMqttService.startSendingFromFile("result.txt", 100);

        // 验证状态变为运行中
        assertTrue(fileToMqttService.isRunning());

        // 停止服务
        fileToMqttService.stop();

        // 验证状态变为未运行
        assertFalse(fileToMqttService.isRunning());
    }

    @Test
    void testMultipleStartAttempts() {
        // 启动服务
        fileToMqttService.startSendingFromFile("result.txt", 100);
        assertTrue(fileToMqttService.isRunning());

        // 尝试再次启动（应该被忽略）
        fileToMqttService.startSendingFromFile("result.txt", 50);
        assertTrue(fileToMqttService.isRunning());

        // 停止服务
        fileToMqttService.stop();
        assertFalse(fileToMqttService.isRunning());
    }
}
