package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 演示如何使用FileToMqttService从文件读取数据并发送到MQTT
 * 
 * 使用方法：
 * 1. 确保MQTT broker正在运行（默认localhost:1883）
 * 2. 确保项目根目录有result.txt文件
 * 3. 运行这个演示类
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.example.demo")
public class FileToMqttDemo implements CommandLineRunner {

    @Autowired
    private FileToMqttService fileToMqttService;

    public static void main(String[] args) {
        SpringApplication.run(FileToMqttDemo.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== File to MQTT Demo ===");
        System.out.println("Starting to read from result.txt and send to MQTT...");
        
        // 演示1：使用默认主题，每50ms发送一行
        System.out.println("\n1. 使用默认主题发送数据（每50ms一行）");
        fileToMqttService.startSendingFromFile("result.txt", 50);
        
        // 等待5秒
        Thread.sleep(5000);
        
        // 停止服务
        fileToMqttService.stop();
        System.out.println("已停止发送");
        
        // 等待一下
        Thread.sleep(1000);
        
        // 演示2：使用自定义主题，每100ms发送一行
        System.out.println("\n2. 使用自定义主题发送数据（每100ms一行）");
        fileToMqttService.startSendingFromFile("result.txt", 100, "demo/sensor/data");
        
        // 等待3秒
        Thread.sleep(3000);
        
        // 停止服务
        fileToMqttService.stop();
        System.out.println("已停止发送");
        
        System.out.println("\n=== Demo 完成 ===");
        System.out.println("你可以通过以下方式监控MQTT消息：");
        System.out.println("1. 使用MQTT客户端订阅主题 'sensor/data' 或 'demo/sensor/data'");
        System.out.println("2. 查看WebSocket页面：http://localhost:8080");
        System.out.println("3. 使用REST API控制服务：");
        System.out.println("   POST /api/file-to-mqtt/start");
        System.out.println("   POST /api/file-to-mqtt/stop");
        System.out.println("   GET  /api/file-to-mqtt/status");
        
        // 退出应用
        System.exit(0);
    }
}
