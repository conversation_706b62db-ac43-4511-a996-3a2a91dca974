package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import com.example.demo.service.MqttPublishService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Scanner;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 演示无限循环读取文件功能
 * 这个演示会持续运行，直到用户手动停止
 */
public class InfiniteFileToMqttDemo {

    @Test
    public void demonstrateInfiniteReading() throws InterruptedException, IOException {
        System.out.println("=== 无限循环文件读取演示 ===");
        System.out.println("⚠️  注意：这个演示会无限运行，直到手动停止！");
        
        // 创建Mock的MQTT发布服务
        MqttPublishService mockMqttService = Mockito.mock(MqttPublishService.class);
        
        // 创建FileToMqttService实例
        FileToMqttService fileToMqttService = new FileToMqttService();
        
        // 使用反射设置mock服务
        try {
            java.lang.reflect.Field field = FileToMqttService.class.getDeclaredField("mqttPublishService");
            field.setAccessible(true);
            field.set(fileToMqttService, mockMqttService);
        } catch (Exception e) {
            System.err.println("无法设置mock服务: " + e.getMessage());
            return;
        }
        
        // 检查result.txt文件
        Path resultFile = Paths.get(System.getProperty("user.dir"), "result.txt");
        if (!Files.exists(resultFile)) {
            System.out.println("❌ result.txt文件不存在: " + resultFile.toAbsolutePath());
            System.out.println("请确保项目根目录下有result.txt文件");
            return;
        }
        
        System.out.println("✅ 找到result.txt文件: " + resultFile.toAbsolutePath());
        System.out.println("📊 文件大小: " + Files.size(resultFile) + " 字节");
        
        // 读取文件信息
        List<String> nonEmptyLines = Files.readAllLines(resultFile)
            .stream()
            .filter(line -> !line.trim().isEmpty())
            .collect(Collectors.toList());
        
        System.out.println("📄 文件总行数: " + nonEmptyLines.size());
        System.out.println("📄 文件前3行内容:");
        for (int i = 0; i < Math.min(3, nonEmptyLines.size()); i++) {
            System.out.println("  " + (i + 1) + ": " + nonEmptyLines.get(i));
        }
        
        System.out.println("\n🔄 开始无限循环读取...");
        System.out.println("⏱️  发送间隔: 100ms");
        System.out.println("🔁 模式: 真正的无限循环（读完整个文件后从第一行重新开始）");
        System.out.println("📈 预计完成一轮循环需要: " + (nonEmptyLines.size() * 100 / 1000.0) + " 秒");
        
        // 启动无限循环读取服务
        fileToMqttService.startSendingFromFile("result.txt", 100);
        
        if (!fileToMqttService.isRunning()) {
            System.out.println("❌ 服务启动失败");
            return;
        }
        
        System.out.println("✅ 服务已启动，开始无限循环发送...");
        System.out.println("\n📊 实时统计信息:");
        
        try {
            // 监控服务运行状态
            long startTime = System.currentTimeMillis();
            long lastReportTime = startTime;
            long lastMessageCount = 0;
            
            while (fileToMqttService.isRunning()) {
                Thread.sleep(5000); // 每5秒报告一次状态
                
                long currentTime = System.currentTimeMillis();
                long currentMessageCount = fileToMqttService.getTotalMessagesSent();
                long currentCycleCount = fileToMqttService.getCycleCount();
                
                // 计算运行时间
                long runningTimeSeconds = (currentTime - startTime) / 1000;
                long minutes = runningTimeSeconds / 60;
                long seconds = runningTimeSeconds % 60;
                
                // 计算发送速率
                long messagesSinceLastReport = currentMessageCount - lastMessageCount;
                double messagesPerSecond = messagesSinceLastReport / 5.0;
                
                // 显示统计信息
                System.out.printf("⏰ 运行时间: %02d:%02d | ", minutes, seconds);
                System.out.printf("📈 总消息: %d | ", currentMessageCount);
                System.out.printf("🔄 完成循环: %d | ", currentCycleCount);
                System.out.printf("⚡ 速率: %.1f 条/秒%n", messagesPerSecond);
                
                // 如果完成了至少一个循环，显示循环信息
                if (currentCycleCount > 0) {
                    long messagesPerCycle = nonEmptyLines.size();
                    long currentCycleProgress = currentMessageCount % messagesPerCycle;
                    double cycleProgress = (currentCycleProgress * 100.0) / messagesPerCycle;
                    System.out.printf("   📊 当前循环进度: %d/%d (%.1f%%)%n", 
                        currentCycleProgress, messagesPerCycle, cycleProgress);
                }
                
                lastReportTime = currentTime;
                lastMessageCount = currentMessageCount;
                
                // 检查是否需要停止（这里可以添加停止条件）
                // 例如：运行超过一定时间后自动停止
                if (runningTimeSeconds > 60) { // 运行1分钟后停止演示
                    System.out.println("\n⏰ 演示时间到（1分钟），停止服务...");
                    break;
                }
            }
            
        } finally {
            // 停止服务
            fileToMqttService.stop();
            System.out.println("🛑 服务已停止");
            
            // 等待服务完全停止
            Thread.sleep(500);
            
            // 显示最终统计
            System.out.println("\n📊 最终统计:");
            System.out.println("📈 总发送消息数: " + fileToMqttService.getTotalMessagesSent());
            System.out.println("🔄 完成循环数: " + fileToMqttService.getCycleCount());
            
            long totalMessages = fileToMqttService.getTotalMessagesSent();
            if (totalMessages > nonEmptyLines.size()) {
                System.out.println("✅ 确认进行了循环读取（消息数 > 文件行数）");
                double avgCycles = (double) totalMessages / nonEmptyLines.size();
                System.out.printf("📊 平均完成了 %.2f 个循环%n", avgCycles);
            }
            
            // 验证消息发送
            verify(mockMqttService, atLeast(1)).publishMessage(anyString());
            System.out.println("✅ 验证通过：成功发送了MQTT消息");
            
            // 关闭服务
            fileToMqttService.shutdown();
        }
        
        System.out.println("\n=== 无限循环演示完成 ===");
        System.out.println("💡 关键特性验证:");
        System.out.println("   ✅ 文件读取完毕后自动从第一行重新开始");
        System.out.println("   ✅ 可以无限循环发送，直到手动停止");
        System.out.println("   ✅ 每完成一个循环都有详细的日志记录");
        System.out.println("   ✅ 实时统计发送速率和循环进度");
        System.out.println("\n🚀 在实际应用中:");
        System.out.println("   • 服务会持续运行，直到调用 stop() 方法");
        System.out.println("   • 可以通过 REST API 随时启动/停止");
        System.out.println("   • 支持实时监控发送状态和统计信息");
    }
    
    /**
     * 交互式演示 - 需要用户手动停止
     * 注意：这个方法在单元测试中不会自动运行
     */
    public void interactiveDemo() throws IOException, InterruptedException {
        System.out.println("=== 交互式无限循环演示 ===");
        System.out.println("按 Enter 键停止服务...");
        
        // 创建服务实例（这里应该使用真实的MQTT服务）
        FileToMqttService fileToMqttService = new FileToMqttService();
        
        // 启动服务
        fileToMqttService.startSendingFromFile("result.txt", 50);
        
        if (fileToMqttService.isRunning()) {
            System.out.println("✅ 服务已启动，正在无限循环发送...");
            System.out.println("📊 实时统计 - 按 Enter 停止:");
            
            // 创建一个线程来监控状态
            Thread monitorThread = new Thread(() -> {
                try {
                    while (fileToMqttService.isRunning()) {
                        Thread.sleep(2000);
                        System.out.printf("\r📈 已发送: %d 条消息 | 🔄 完成循环: %d 次", 
                            fileToMqttService.getTotalMessagesSent(),
                            fileToMqttService.getCycleCount());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            
            monitorThread.start();
            
            // 等待用户输入
            Scanner scanner = new Scanner(System.in);
            scanner.nextLine();
            
            // 停止服务
            fileToMqttService.stop();
            monitorThread.interrupt();
            
            System.out.println("\n🛑 服务已停止");
            System.out.println("📊 最终统计:");
            System.out.println("   总消息数: " + fileToMqttService.getTotalMessagesSent());
            System.out.println("   完成循环: " + fileToMqttService.getCycleCount());
        }
        
        fileToMqttService.shutdown();
    }
}
