package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 简单的文件到MQTT循环读取测试
 * 按行每50ms读取result.txt内容发送到MQTT，读完后从第一行重新开始
 */
@SpringBootTest(classes = com.example.demo.MqttWebsocketDemoApplication.class)
public class SimpleFileToMqttTest {

    @Autowired
    private FileToMqttService fileToMqttService;

    @Test
    public void testCyclicFileToMqtt() throws IOException, InterruptedException {
        System.out.println("=== 开始循环读取result.txt发送到MQTT ===");

        // 检查result.txt文件
        Path resultFile = Paths.get(System.getProperty("user.dir"), "result.txt");
        if (!Files.exists(resultFile)) {
            System.out.println("❌ result.txt文件不存在: " + resultFile.toAbsolutePath());
            return;
        }

        System.out.println("✅ 文件路径: " + resultFile.toAbsolutePath());
        System.out.println("📊 文件大小: " + Files.size(resultFile) + " 字节");

        // 启动循环读取服务，每50ms读取一行
        System.out.println("🚀 启动服务：每50ms读取一行，循环发送到MQTT");
        fileToMqttService.startSendingFromFile("result.txt", 50);

        if (!fileToMqttService.isRunning()) {
            System.out.println("❌ 服务启动失败");
            return;
        }

        System.out.println("✅ 服务已启动，正在循环发送...");
        System.out.println("⏳ 运行10秒后自动停止...");

        // 启动监控线程
        Thread monitorThread = new Thread(() -> {
            try {
                while (fileToMqttService.isRunning()) {
                    Thread.sleep(2000); // 每2秒显示一次状态
                    System.out.printf("📈 已发送: %d 条消息 | 🔄 完成循环: %d 次%n",
                        fileToMqttService.getTotalMessagesSent(),
                        fileToMqttService.getCycleCount());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        monitorThread.setDaemon(true);
        monitorThread.start();

        // 运行10秒后自动停止
        Thread.sleep(10000);

        // 停止服务
        System.out.println("🛑 正在停止服务...");
        fileToMqttService.stop();

        // 等待服务完全停止
        Thread.sleep(1000);

        // 显示最终统计
        System.out.println("📊 最终统计:");
        System.out.println("   总发送消息数: " + fileToMqttService.getTotalMessagesSent());
        System.out.println("   完成循环数: " + fileToMqttService.getCycleCount());
        System.out.println("=== 测试完成 ===");
    }
}
