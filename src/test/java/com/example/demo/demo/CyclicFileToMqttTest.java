package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import com.example.demo.service.MqttPublishService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 测试循环读取文件功能
 * 验证文件读取完毕后会自动从第一行重新开始读取
 */
public class CyclicFileToMqttTest {

    @Test
    public void testCyclicFileReading() throws InterruptedException, IOException {
        System.out.println("=== 循环文件读取功能演示 ===");

        // 创建Mock的MQTT发布服务
        MqttPublishService mockMqttService = Mockito.mock(MqttPublishService.class);

        // 创建FileToMqttService实例
        FileToMqttService fileToMqttService = new FileToMqttService();

        // 使用反射设置mock服务
        try {
            java.lang.reflect.Field field = FileToMqttService.class.getDeclaredField("mqttPublishService");
            field.setAccessible(true);
            field.set(fileToMqttService, mockMqttService);
        } catch (Exception e) {
            System.err.println("无法设置mock服务: " + e.getMessage());
            return;
        }

        // 直接使用当前路径的result.txt文件
        Path resultFile = Paths.get(System.getProperty("user.dir"), "result.txt");

        // 检查result.txt文件是否存在
        if (!Files.exists(resultFile)) {
            System.out.println("❌ result.txt文件不存在: " + resultFile.toAbsolutePath());
            System.out.println("请确保项目根目录下有result.txt文件");
            return;
        }

        System.out.println("✅ 找到result.txt文件: " + resultFile.toAbsolutePath());
        System.out.println("📊 文件大小: " + Files.size(resultFile) + " 字节");

        // 读取文件前几行作为参考
        try {
            List<String> sampleLines = Files.readAllLines(resultFile)
                .stream()
                .filter(line -> !line.trim().isEmpty())
                .limit(5)
                .collect(Collectors.toList());

            System.out.println("📄 文件前几行内容:");
            for (int i = 0; i < sampleLines.size(); i++) {
                System.out.println("  " + (i + 1) + ": " + sampleLines.get(i));
            }

        } catch (IOException e) {
            System.err.println("读取文件内容失败: " + e.getMessage());
            return;
        }

        try {
            // 启动循环读取服务，每50ms读取一行
            System.out.println("\n🔄 开始循环读取result.txt文件...");
            System.out.println("⏱️  发送间隔: 50ms");
            System.out.println("🔁 模式: 循环读取（读完后从第一行重新开始）");

            fileToMqttService.startSendingFromFile("result.txt", 50);

            if (fileToMqttService.isRunning()) {
                System.out.println("✅ 服务已启动");
            } else {
                System.out.println("❌ 服务启动失败");
                return;
            }

            // 让服务运行3秒，观察循环读取效果
            System.out.println("⏳ 运行3秒，观察循环读取效果...");
            Thread.sleep(3000);

            // 停止服务
            fileToMqttService.stop();
            System.out.println("🛑 服务已停止");

            // 等待一下确保服务完全停止
            Thread.sleep(200);

            // 验证结果
            System.out.println("\n📊 验证结果:");

            // 获取调用次数
            int totalCalls = mockingDetails(mockMqttService).getInvocations().size();
            System.out.println("📈 总共发送了 " + totalCalls + " 条消息");
            System.out.println("⏱️  平均发送速率: " + String.format("%.1f", totalCalls / 3.0) + " 条/秒");

            // 验证至少发送了一些消息
            if (totalCalls > 0) {
                System.out.println("✅ 循环读取功能正常工作");

                // 估算文件行数和循环次数
                long estimatedFileLines = Files.readAllLines(resultFile)
                    .stream()
                    .filter(line -> !line.trim().isEmpty())
                    .count();

                System.out.println("📄 文件估算行数: " + estimatedFileLines);

                if (totalCalls > estimatedFileLines) {
                    int completeCycles = (int) (totalCalls / estimatedFileLines);
                    int remainingMessages = (int) (totalCalls % estimatedFileLines);

                    System.out.println("🔄 估算完成了 " + completeCycles + " 个完整循环");
                    if (remainingMessages > 0) {
                        System.out.println("➕ 额外发送了 " + remainingMessages + " 条消息");
                    }
                    System.out.println("✅ 确认进行了循环读取（消息数量 > 文件行数）");
                } else {
                    System.out.println("⚠️  可能还未完成一个完整循环，或文件较大");
                }
            } else {
                System.out.println("❌ 没有发送任何消息");
            }

            // 验证消息发送
            verify(mockMqttService, atLeast(1)).publishMessage(anyString());
            System.out.println("✅ 验证通过：成功发送了MQTT消息");

        } finally {
            // 关闭服务
            fileToMqttService.shutdown();
            System.out.println("🧹 服务已关闭");
        }

        System.out.println("\n=== 循环读取演示完成 ===");
        System.out.println("💡 循环读取特性:");
        System.out.println("   1. 文件读取完毕后自动从第一行重新开始");
        System.out.println("   2. 可以无限循环发送，直到手动停止");
        System.out.println("   3. 适合需要持续发送测试数据的场景");
        System.out.println("   4. 每完成一个循环会在日志中记录");
    }
}
