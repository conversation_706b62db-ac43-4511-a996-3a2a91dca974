package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import com.example.demo.service.MqttPublishService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 手动测试FileToMqttService功能
 * 这个测试演示了如何从result.txt文件读取数据并发送到MQTT
 */
public class ManualFileToMqttTest {

    @Test
    public void testReadFromResultTxtFile() throws InterruptedException, IOException {
        System.out.println("=== 文件到MQTT功能演示 ===");

        // 创建Mock的MQTT发布服务
        MqttPublishService mockMqttService = Mockito.mock(MqttPublishService.class);

        // 创建FileToMqttService实例
        FileToMqttService fileToMqttService = new FileToMqttService();

        // 使用反射设置mock服务（在实际应用中会通过Spring注入）
        try {
            java.lang.reflect.Field field = FileToMqttService.class.getDeclaredField("mqttPublishService");
            field.setAccessible(true);
            field.set(fileToMqttService, mockMqttService);
        } catch (Exception e) {
            System.err.println("无法设置mock服务: " + e.getMessage());
            return;
        }

        // 检查项目根目录的result.txt文件是否存在
        Path resultFile = Paths.get(System.getProperty("user.dir"), "result.txt");
        if (!Files.exists(resultFile)) {
            System.out.println("❌ result.txt文件不存在，创建示例文件...");

            // 创建示例数据
            List<String> sampleData = Arrays.asList(
                "{\"timestamp\": \"2024-01-01T10:00:00Z\", \"sensor\": \"temperature\", \"value\": 23.5}",
                "{\"timestamp\": \"2024-01-01T10:00:01Z\", \"sensor\": \"humidity\", \"value\": 65.2}",
                "{\"timestamp\": \"2024-01-01T10:00:02Z\", \"sensor\": \"pressure\", \"value\": 1013.25}",
                "{\"timestamp\": \"2024-01-01T10:00:03Z\", \"sensor\": \"temperature\", \"value\": 23.6}",
                "{\"timestamp\": \"2024-01-01T10:00:04Z\", \"sensor\": \"humidity\", \"value\": 65.0}"
            );

            Files.write(resultFile, sampleData);
            System.out.println("✅ 已创建示例result.txt文件");
        } else {
            System.out.println("✅ 找到result.txt文件");
        }

        System.out.println("📁 文件路径: " + resultFile.toAbsolutePath());
        System.out.println("📊 文件大小: " + Files.size(resultFile) + " 字节");

        // 启动文件读取服务，每50ms读取一行
        System.out.println("\n🚀 开始从文件读取数据并发送到MQTT...");
        System.out.println("⏱️  发送间隔: 50ms");
        System.out.println("📡 MQTT主题: sensor/data (默认)");

        fileToMqttService.startSendingFromFile("result.txt", 50);

        // 检查服务状态
        if (fileToMqttService.isRunning()) {
            System.out.println("✅ 服务已启动，正在发送数据...");
        } else {
            System.out.println("❌ 服务启动失败");
            return;
        }

        // 让服务运行3秒
        System.out.println("⏳ 运行3秒...");
        Thread.sleep(3000);

        // 停止服务
        fileToMqttService.stop();
        System.out.println("🛑 服务已停止");

        // 等待一下确保服务完全停止
        Thread.sleep(500);

        // 验证MQTT消息发送
        System.out.println("\n📈 验证结果:");
        try {
            verify(mockMqttService, atLeast(1)).publishMessage(anyString());
            System.out.println("✅ 成功发送了MQTT消息");

            // 获取调用次数
            int callCount = mockingDetails(mockMqttService).getInvocations().size();
            System.out.println("📊 总共发送了 " + callCount + " 条消息");

        } catch (Exception e) {
            System.out.println("❌ 验证失败: " + e.getMessage());
        }

        // 关闭服务
        fileToMqttService.shutdown();

        System.out.println("\n=== 演示完成 ===");
        System.out.println("💡 在实际应用中:");
        System.out.println("   1. MQTT消息会发送到真实的MQTT broker");
        System.out.println("   2. 可以通过REST API控制服务: /api/file-to-mqtt/start");
        System.out.println("   3. 可以通过WebSocket实时查看消息: http://localhost:8080");
    }
}
