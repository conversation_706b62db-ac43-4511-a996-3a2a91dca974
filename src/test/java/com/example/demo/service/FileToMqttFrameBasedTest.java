package com.example.demo.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class FileToMqttFrameBasedTest {

    @Mock
    private MqttPublishService mqttPublishService;

    @InjectMocks
    private FileToMqttService fileToMqttService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testFrameBasedSending_SameIdGroupedTogether() throws IOException, InterruptedException {
        // 创建测试文件，包含按ID分组的数据
        Path testFile = tempDir.resolve("frame_test.txt");
        List<String> lines = Arrays.asList(
            "1,1,768,1276,316,104,18,15,0.82,2",
            "1,2,785,872,337,91,13,12,0.73,2",
            "1,3,532,971,211,93,17,13,0.71,2",
            "1,4,508,1468,105,115,31,16,0.70,2",
            "2,1,768,1257,317,103,17,15,0.78,2",
            "2,2,785,872,337,91,13,12,0.79,2",
            "2,3,529,971,209,94,18,12,0.56,2",
            "2,4,509,1480,101,115,33,17,0.77,7"
        );
        Files.write(testFile, lines);

        String customTopic = "test/frame/topic";

        // 启动服务 - 使用绝对路径
        fileToMqttService.startSendingFromFile(testFile.toAbsolutePath().toString(), 50, customTopic);

        // 等待服务处理文件
        Thread.sleep(300); // 等待足够时间处理两个帧

        // 停止服务
        fileToMqttService.stop();

        // 验证发送了两个帧
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(mqttPublishService, times(2)).publishMessage(eq(customTopic), messageCaptor.capture());

        List<String> sentMessages = messageCaptor.getAllValues();

        // 验证第一个帧包含所有ID为1的行
        String frame1 = sentMessages.get(0);
        assertTrue(frame1.contains("1,1,768,1276,316,104,18,15,0.82,2"));
        assertTrue(frame1.contains("1,2,785,872,337,91,13,12,0.73,2"));
        assertTrue(frame1.contains("1,3,532,971,211,93,17,13,0.71,2"));
        assertTrue(frame1.contains("1,4,508,1468,105,115,31,16,0.70,2"));

        // 验证第二个帧包含所有ID为2的行
        String frame2 = sentMessages.get(1);
        assertTrue(frame2.contains("2,1,768,1257,317,103,17,15,0.78,2"));
        assertTrue(frame2.contains("2,2,785,872,337,91,13,12,0.79,2"));
        assertTrue(frame2.contains("2,3,529,971,209,94,18,12,0.56,2"));
        assertTrue(frame2.contains("2,4,509,1480,101,115,33,17,0.77,7"));

        // 验证帧内行数正确
        assertEquals(4, frame1.split("\n").length, "Frame 1 should contain 4 lines");
        assertEquals(4, frame2.split("\n").length, "Frame 2 should contain 4 lines");
    }

    @Test
    void testFrameBasedSending_MixedIds() throws IOException, InterruptedException {
        // 创建测试文件，包含混合ID的数据
        Path testFile = tempDir.resolve("mixed_frame_test.txt");
        List<String> lines = Arrays.asList(
            "A,data1",
            "A,data2",
            "B,data3",
            "A,data4",
            "A,data5",
            "C,data6"
        );
        Files.write(testFile, lines);

        // 启动服务 - 使用绝对路径
        fileToMqttService.startSendingFromFile(testFile.toAbsolutePath().toString(), 30);

        // 等待服务处理文件
        Thread.sleep(200);

        // 停止服务
        fileToMqttService.stop();

        // 验证发送了4个帧（A, B, A, C）
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(mqttPublishService, times(4)).publishMessage(messageCaptor.capture());

        List<String> sentMessages = messageCaptor.getAllValues();

        // 验证第一个帧（A）
        String frame1 = sentMessages.get(0);
        assertTrue(frame1.contains("A,data1"));
        assertTrue(frame1.contains("A,data2"));
        assertEquals(2, frame1.split("\n").length);

        // 验证第二个帧（B）
        String frame2 = sentMessages.get(1);
        assertTrue(frame2.contains("B,data3"));
        assertEquals(1, frame2.split("\n").length);

        // 验证第三个帧（A）
        String frame3 = sentMessages.get(2);
        assertTrue(frame3.contains("A,data4"));
        assertTrue(frame3.contains("A,data5"));
        assertEquals(2, frame3.split("\n").length);

        // 验证第四个帧（C）
        String frame4 = sentMessages.get(3);
        assertTrue(frame4.contains("C,data6"));
        assertEquals(1, frame4.split("\n").length);
    }

    @Test
    void testFrameBasedSending_SingleLineFrames() throws IOException, InterruptedException {
        // 创建测试文件，每行都是不同ID
        Path testFile = tempDir.resolve("single_frame_test.txt");
        List<String> lines = Arrays.asList(
            "1,single1",
            "2,single2",
            "3,single3"
        );
        Files.write(testFile, lines);

        // 启动服务 - 使用绝对路径
        fileToMqttService.startSendingFromFile(testFile.toAbsolutePath().toString(), 20);

        // 等待服务处理文件
        Thread.sleep(150);

        // 停止服务
        fileToMqttService.stop();

        // 验证发送了3个帧，每个帧只有一行
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(mqttPublishService, times(3)).publishMessage(messageCaptor.capture());

        List<String> sentMessages = messageCaptor.getAllValues();

        assertEquals("1,single1", sentMessages.get(0));
        assertEquals("2,single2", sentMessages.get(1));
        assertEquals("3,single3", sentMessages.get(2));
    }
}
