package com.example.demo.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class FileToMqttServiceTest {

    @Mock
    private MqttPublishService mqttPublishService;

    @InjectMocks
    private FileToMqttService fileToMqttService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testStartSendingFromFile_WithDefaultTopic() throws IOException, InterruptedException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        List<String> lines = Arrays.asList("line1", "line2", "line3");
        Files.write(testFile, lines);

        // 启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 10);

        // 等待一段时间让服务处理文件
        Thread.sleep(100);

        // 验证是否正在运行
        assertTrue(fileToMqttService.isRunning());

        // 等待服务完成
        Thread.sleep(200);

        // 验证MQTT消息发送
        verify(mqttPublishService, times(3)).publishMessage(anyString());
        verify(mqttPublishService).publishMessage("line1");
        verify(mqttPublishService).publishMessage("line2");
        verify(mqttPublishService).publishMessage("line3");

        // 验证服务已停止
        assertFalse(fileToMqttService.isRunning());
    }

    @Test
    void testStartSendingFromFile_WithCustomTopic() throws IOException, InterruptedException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        List<String> lines = Arrays.asList("data1", "data2");
        Files.write(testFile, lines);

        String customTopic = "test/topic";

        // 启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 10, customTopic);

        // 等待服务处理文件
        Thread.sleep(100);

        // 验证MQTT消息发送到指定主题
        verify(mqttPublishService, times(2)).publishMessage(eq(customTopic), anyString());
        verify(mqttPublishService).publishMessage(customTopic, "data1");
        verify(mqttPublishService).publishMessage(customTopic, "data2");
    }

    @Test
    void testStartSendingFromFile_FileNotExists() {
        String nonExistentFile = "non_existent_file.txt";

        // 启动服务
        fileToMqttService.startSendingFromFile(nonExistentFile, 50);

        // 验证服务没有启动
        assertFalse(fileToMqttService.isRunning());

        // 验证没有发送任何消息
        verifyNoInteractions(mqttPublishService);
    }

    @Test
    void testStartSendingFromFile_EmptyLines() throws IOException, InterruptedException {
        // 创建包含空行的测试文件
        Path testFile = tempDir.resolve("test_with_empty_lines.txt");
        List<String> lines = Arrays.asList("line1", "", "line2", "   ", "line3");
        Files.write(testFile, lines);

        // 启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 10);

        // 等待服务处理文件
        Thread.sleep(200);

        // 验证只发送了非空行
        verify(mqttPublishService, times(3)).publishMessage(anyString());
        verify(mqttPublishService).publishMessage("line1");
        verify(mqttPublishService).publishMessage("line2");
        verify(mqttPublishService).publishMessage("line3");
    }

    @Test
    void testStopService() throws IOException, InterruptedException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        List<String> lines = Arrays.asList("line1", "line2", "line3", "line4", "line5");
        Files.write(testFile, lines);

        // 启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 50);

        // 验证服务正在运行
        assertTrue(fileToMqttService.isRunning());

        // 等待一小段时间然后停止服务
        Thread.sleep(30);
        fileToMqttService.stop();

        // 验证服务已停止
        assertFalse(fileToMqttService.isRunning());
    }

    @Test
    void testServiceAlreadyRunning() throws IOException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        List<String> lines = Arrays.asList("line1", "line2");
        Files.write(testFile, lines);

        // 启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 100);

        // 尝试再次启动服务
        fileToMqttService.startSendingFromFile(testFile.toString(), 100);

        // 验证服务仍在运行
        assertTrue(fileToMqttService.isRunning());

        // 清理
        fileToMqttService.stop();
    }
}
