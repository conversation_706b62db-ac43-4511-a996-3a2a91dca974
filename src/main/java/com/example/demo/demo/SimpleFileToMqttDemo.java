package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import com.example.demo.service.MqttPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 简单的文件到MQTT演示程序
 * 
 * 这个演示程序会：
 * 1. 从result.txt文件读取数据
 * 2. 每50ms发送一行到MQTT
 * 3. 运行10秒后停止
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.example.demo")
public class SimpleFileToMqttDemo implements CommandLineRunner {

    @Autowired
    private FileToMqttService fileToMqttService;

    public static void main(String[] args) {
        System.setProperty("spring.main.web-application-type", "none");
        SpringApplication.run(SimpleFileToMqttDemo.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
//        System.out.println("=== 简单文件到MQTT演示 ===");
//        System.out.println("开始从result.txt读取数据并发送到MQTT...");
//        System.out.println("发送间隔：50ms");
//        System.out.println("MQTT主题：sensor/data（默认）");
//        System.out.println();
//
//        // 启动文件读取服务
//        fileToMqttService.startSendingFromFile("result.txt", 50);
//
//        // 检查服务状态
//        if (fileToMqttService.isRunning()) {
//            System.out.println("✅ 服务已启动，正在发送数据...");
//        } else {
//            System.out.println("❌ 服务启动失败，请检查result.txt文件是否存在");
//            return;
//        }
//
//        // 运行10秒
//        System.out.println("将运行10秒，然后停止...");
//        Thread.sleep(10000);
//
//        // 停止服务
//        fileToMqttService.stop();
//        System.out.println("✅ 服务已停止");
//
//        System.out.println();
//        System.out.println("=== 演示完成 ===");
//        System.out.println("如果你有MQTT客户端，可以订阅 'sensor/data' 主题查看发送的消息");
//
//        // 关闭服务
//        fileToMqttService.shutdown();
    }
}
