package com.example.demo.demo;

import com.example.demo.service.FileToMqttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Scanner;
import java.util.stream.Collectors;

/**
 * 无限循环文件读取启动器
 * 演示真正的无限循环发送功能
 */
@Component
public class InfiniteFileToMqttStarter implements CommandLineRunner {

    @Autowired
    private FileToMqttService fileToMqttService;

    @Override
    public void run(String... args) throws Exception {
        // 检查是否有启动参数
        boolean startInfiniteDemo = false;
        for (String arg : args) {
            if ("--infinite-demo".equals(arg)) {
                startInfiniteDemo = true;
                break;
            }
        }

        if (!startInfiniteDemo) {
            return; // 不启动演示
        }

        System.out.println("=== 🔄 无限循环文件读取演示 ===");
        System.out.println("⚠️  注意：这将启动真正的无限循环发送！");
        
        // 检查result.txt文件
        Path resultFile = Paths.get(System.getProperty("user.dir"), "result.txt");
        if (!Files.exists(resultFile)) {
            System.out.println("❌ result.txt文件不存在: " + resultFile.toAbsolutePath());
            System.out.println("请确保项目根目录下有result.txt文件");
            return;
        }

        System.out.println("✅ 找到result.txt文件: " + resultFile.toAbsolutePath());
        System.out.println("📊 文件大小: " + Files.size(resultFile) + " 字节");

        // 读取文件信息
        List<String> nonEmptyLines = Files.readAllLines(resultFile)
            .stream()
            .filter(line -> !line.trim().isEmpty())
            .collect(Collectors.toList());

        System.out.println("📄 文件总行数: " + nonEmptyLines.size());
        System.out.println("📄 文件前3行内容:");
        for (int i = 0; i < Math.min(3, nonEmptyLines.size()); i++) {
            System.out.println("  " + (i + 1) + ": " + nonEmptyLines.get(i));
        }

        System.out.println("\n🔄 配置信息:");
        System.out.println("⏱️  发送间隔: 50ms");
        System.out.println("📡 MQTT主题: sensor/data (默认)");
        System.out.println("🔁 模式: 真正的无限循环（读完整个文件后从第一行重新开始）");
        System.out.printf("📈 预计完成一轮循环需要: %.1f 秒%n", (nonEmptyLines.size() * 50 / 1000.0));

        System.out.println("\n❓ 是否开始无限循环发送？(y/n): ");
        Scanner scanner = new Scanner(System.in);
        String input = scanner.nextLine().trim().toLowerCase();

        if (!"y".equals(input) && !"yes".equals(input)) {
            System.out.println("❌ 用户取消，退出演示");
            return;
        }

        // 启动无限循环读取服务
        System.out.println("\n🚀 启动无限循环发送服务...");
        fileToMqttService.startSendingFromFile("result.txt", 50);

        if (!fileToMqttService.isRunning()) {
            System.out.println("❌ 服务启动失败");
            return;
        }

        System.out.println("✅ 服务已启动，开始无限循环发送...");
        System.out.println("📊 实时统计信息 (每5秒更新一次):");
        System.out.println("💡 按 'q' + Enter 停止服务");

        // 启动监控线程
        Thread monitorThread = new Thread(() -> {
            try {
                long startTime = System.currentTimeMillis();
                long lastMessageCount = 0;

                while (fileToMqttService.isRunning()) {
                    Thread.sleep(5000); // 每5秒报告一次

                    long currentTime = System.currentTimeMillis();
                    long currentMessageCount = fileToMqttService.getTotalMessagesSent();
                    long currentCycleCount = fileToMqttService.getCycleCount();

                    // 计算运行时间
                    long runningTimeSeconds = (currentTime - startTime) / 1000;
                    long hours = runningTimeSeconds / 3600;
                    long minutes = (runningTimeSeconds % 3600) / 60;
                    long seconds = runningTimeSeconds % 60;

                    // 计算发送速率
                    long messagesSinceLastReport = currentMessageCount - lastMessageCount;
                    double messagesPerSecond = messagesSinceLastReport / 5.0;

                    // 显示统计信息
                    System.out.printf("⏰ 运行时间: %02d:%02d:%02d | ", hours, minutes, seconds);
                    System.out.printf("📈 总消息: %d | ", currentMessageCount);
                    System.out.printf("🔄 完成循环: %d | ", currentCycleCount);
                    System.out.printf("⚡ 速率: %.1f 条/秒%n", messagesPerSecond);

                    // 如果完成了至少一个循环，显示循环信息
                    if (currentCycleCount > 0) {
                        long messagesPerCycle = nonEmptyLines.size();
                        long currentCycleProgress = currentMessageCount % messagesPerCycle;
                        if (currentCycleProgress == 0 && currentMessageCount > 0) {
                            currentCycleProgress = messagesPerCycle; // 刚完成一个循环
                        }
                        double cycleProgress = (currentCycleProgress * 100.0) / messagesPerCycle;
                        System.out.printf("   📊 当前循环进度: %d/%d (%.1f%%) | ", 
                            currentCycleProgress, messagesPerCycle, cycleProgress);
                        
                        // 估算完成当前循环的剩余时间
                        if (messagesPerSecond > 0) {
                            long remainingMessages = messagesPerCycle - currentCycleProgress;
                            double remainingSeconds = remainingMessages / messagesPerSecond;
                            System.out.printf("⏳ 本轮剩余: %.0f秒%n", remainingSeconds);
                        } else {
                            System.out.println();
                        }
                    }

                    lastMessageCount = currentMessageCount;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        monitorThread.setDaemon(true);
        monitorThread.start();

        // 等待用户输入停止命令
        System.out.println("\n💡 输入 'q' 并按 Enter 停止服务:");
        while (fileToMqttService.isRunning()) {
            String command = scanner.nextLine().trim().toLowerCase();
            if ("q".equals(command) || "quit".equals(command) || "stop".equals(command)) {
                break;
            } else if ("status".equals(command) || "s".equals(command)) {
                // 显示详细状态
                System.out.println("\n📊 详细状态信息:");
                System.out.println("   🔄 服务运行中: " + fileToMqttService.isRunning());
                System.out.println("   📈 总发送消息: " + fileToMqttService.getTotalMessagesSent());
                System.out.println("   🔄 完成循环数: " + fileToMqttService.getCycleCount());
                System.out.println("   📄 文件总行数: " + nonEmptyLines.size());
                System.out.println("💡 输入 'q' 停止服务");
            } else if ("help".equals(command) || "h".equals(command)) {
                System.out.println("\n💡 可用命令:");
                System.out.println("   q, quit, stop - 停止服务");
                System.out.println("   s, status     - 显示详细状态");
                System.out.println("   h, help       - 显示帮助");
            } else {
                System.out.println("❓ 未知命令，输入 'h' 查看帮助，输入 'q' 停止服务");
            }
        }

        // 停止服务
        System.out.println("\n🛑 正在停止服务...");
        fileToMqttService.stop();
        monitorThread.interrupt();

        // 等待服务完全停止
        Thread.sleep(1000);

        // 显示最终统计
        System.out.println("\n📊 最终统计信息:");
        System.out.println("📈 总发送消息数: " + fileToMqttService.getTotalMessagesSent());
        System.out.println("🔄 完成循环数: " + fileToMqttService.getCycleCount());

        long totalMessages = fileToMqttService.getTotalMessagesSent();
        if (totalMessages > nonEmptyLines.size()) {
            System.out.println("✅ 确认进行了循环读取（消息数 > 文件行数）");
            double avgCycles = (double) totalMessages / nonEmptyLines.size();
            System.out.printf("📊 平均完成了 %.2f 个循环%n", avgCycles);
        }

        System.out.println("\n=== 🎉 无限循环演示完成 ===");
        System.out.println("💡 关键特性验证:");
        System.out.println("   ✅ 文件读取完毕后自动从第一行重新开始");
        System.out.println("   ✅ 可以无限循环发送，直到手动停止");
        System.out.println("   ✅ 每完成一个循环都有详细的日志记录");
        System.out.println("   ✅ 实时统计发送速率和循环进度");
        
        scanner.close();
    }
}
