package com.example.demo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

@Service
public class MqttMessageHandler {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @ServiceActivator(inputChannel = "mqttInputChannel")
    public void handleMessage(Message<?> message) {
        String payload = message.getPayload().toString();
        // 这里可以添加解析逻辑，例如JSON解析等
        System.out.println("Received message: " + payload);
        
        // 将消息推送到WebSocket
        messagingTemplate.convertAndSend("/topic/messages", payload);
    }
}