package com.example.demo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

@Service
public class MqttPublishService {

    @Autowired
    private MessageChannel mqttOutboundChannel;

    public void publishMessage(String message) {
        mqttOutboundChannel.send(MessageBuilder.withPayload(message).build());
    }

    public void publishMessage(String topic, String message) {
        mqttOutboundChannel.send(
            MessageBuilder.withPayload(message)
                .setHeader("mqtt_topic", topic)
                .build()
        );
    }
}
