package com.example.demo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
public class FileToMqttService {

    private static final Logger logger = LoggerFactory.getLogger(FileToMqttService.class);
    @Value("${mqtt.topic}")
    private String topic;
    @Autowired
    private MqttPublishService mqttPublishService;

    private volatile ScheduledExecutorService scheduler;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong cycleCount = new AtomicLong(0);
    private final AtomicLong totalMessagesSent = new AtomicLong(0);

    // 初始化线程池
    private void initializeScheduler() {
        if (scheduler == null || scheduler.isShutdown()) {
            scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "FileToMqtt-Worker");
                t.setDaemon(true);
                return t;
            });
        }
    }

    /**
     * 从指定文件按帧读取数据并发送到MQTT（循环读取）
     * 按行读取，每行的第一个字符是ID，相同的ID为一帧
     * 当读取到不同的ID时，发送该ID下的所有数据，然后休眠指定时间，再进行下一次读取
     * @param filePath 文件路径（相对于项目根目录）
     * @param intervalMs 发送间隔（毫秒）
     * @param topic MQTT主题（可选，为null时使用默认主题）
     */
    public void startSendingFromFile(String filePath, long intervalMs, String topic) {
        if (isRunning.get()) {
            logger.warn("File to MQTT service is already running");
            return;
        }

        // 确保使用项目根目录的文件路径
        Path path = Paths.get(System.getProperty("user.dir"), filePath);
        if (!Files.exists(path)) {
            logger.error("File does not exist: {}", path.toAbsolutePath());
            return;
        }

        // 初始化或重新创建线程池
        initializeScheduler();

        isRunning.set(true);
        cycleCount.set(0);
        totalMessagesSent.set(0);
        logger.info("Starting to send data from file: {} with interval: {}ms (帧模式循环)", path.toAbsolutePath(), intervalMs);

        scheduler.submit(() -> {
            try {
                // 一次性读取所有非空行到内存中
                List<String> nonEmptyLines = Files.readAllLines(path)
                    .stream()
                    .filter(line -> !line.trim().isEmpty())
                    .collect(Collectors.toList());

                if (nonEmptyLines.isEmpty()) {
                    logger.warn("File is empty or contains no non-empty lines: {}", path.toAbsolutePath());
                    stop();
                    return;
                }

                logger.info("Loaded {} non-empty lines from file, starting infinite cyclic frame-based reading...", nonEmptyLines.size());

                int currentIndex = 0;
                long currentCycle = 1;
                long frameCount = 0;

                while (isRunning.get()) {
                    // 如果是新的一轮循环，记录日志
                    if (currentIndex == 0 && totalMessagesSent.get() > 0) {
                        currentCycle++;
                        cycleCount.set(currentCycle);
                        logger.info("🔄 开始第 {} 轮循环读取", currentCycle);
                    }

                    // 读取当前行并获取ID（第一个字符）
                    String currentLine = nonEmptyLines.get(currentIndex);
                    if (currentLine.isEmpty()) {
                        currentIndex = (currentIndex + 1) % nonEmptyLines.size();
                        continue;
                    }
                    
                    char currentId = currentLine.charAt(0);
                    List<String> frameLines = new ArrayList<>();
                    frameLines.add(currentLine);
                    
                    // 继续读取相同ID的行
                    int nextIndex = (currentIndex + 1) % nonEmptyLines.size();
                    boolean isNewCycle = false;
                    
                    while (isRunning.get()) {
                        // 检查是否回到文件开头（新的循环）
                        if (nextIndex == 0 && currentIndex != nonEmptyLines.size() - 1) {
                            isNewCycle = true;
                        }
                        
                        String nextLine = nonEmptyLines.get(nextIndex);
                        if (nextLine.isEmpty()) {
                            nextIndex = (nextIndex + 1) % nonEmptyLines.size();
                            continue;
                        }
                        
                        char nextId = nextLine.charAt(0);
                        
                        // 如果ID相同，添加到当前帧
                        if (nextId == currentId) {
                            frameLines.add(nextLine);
                            nextIndex = (nextIndex + 1) % nonEmptyLines.size();
                        } else {
                            // ID不同，结束当前帧
                            break;
                        }
                        
                        // 如果到达文件末尾，也结束当前帧
                        if (nextIndex == 0) {
                            isNewCycle = true;
                            break;
                        }
                    }
                    
                    // 发送整个帧（所有相同ID的行）
                    String frameMessage = String.join("\n", frameLines);
                    
                    if (topic != null) {
                        mqttPublishService.publishMessage(topic, frameMessage);
                    } else {
                        mqttPublishService.publishMessage(frameMessage);
                    }

                    frameCount++;
                    long messageCount = totalMessagesSent.addAndGet(frameLines.size());
                    logger.debug("Sent frame #{} with ID '{}' containing {} lines (cycle {}, total messages: {})",
                        frameCount, currentId, frameLines.size(), currentCycle, messageCount);

                    // 等待指定的间隔时间（50ms）
                    Thread.sleep(intervalMs);

                    // 移动到下一个不同ID的位置
                    currentIndex = nextIndex;
                    
                    // 如果完成了一个完整循环，更新循环计数
                    if (isNewCycle || currentIndex == 0) {
                        cycleCount.set(currentCycle);
                        logger.info("✅ 完成第 {} 轮循环，共发送 {} 个帧，{} 条消息", currentCycle, frameCount, messageCount);
                        if (currentIndex == 0) {
                            frameCount = 0; // 重置帧计数
                        }
                    }
                }

                logger.info("File to MQTT service stopped after {} cycles, {} total frames sent",
                    cycleCount.get(), frameCount);

            } catch (IOException e) {
                logger.error("Error reading file: {}", path.toAbsolutePath(), e);
                stop();
            } catch (InterruptedException e) {
                logger.info("File to MQTT service was interrupted after {} cycles",
                    cycleCount.get());
                Thread.currentThread().interrupt();
                stop();
            }
        });
    }

    /**
     * 使用默认主题发送
     */
    public void startSendingFromFile(String filePath, long intervalMs) {
        startSendingFromFile(filePath, intervalMs, topic);
    }

    /**
     * 停止发送服务
     */
    public void stop() {
        if (isRunning.get()) {
            isRunning.set(false);
            logger.info("File to MQTT service stopped");
        }
    }

    /**
     * 检查服务是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }

    /**
     * 获取已完成的循环次数
     */
    public long getCycleCount() {
        return cycleCount.get();
    }

    /**
     * 获取总发送消息数
     */
    public long getTotalMessagesSent() {
        return totalMessagesSent.get();
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        stop();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
