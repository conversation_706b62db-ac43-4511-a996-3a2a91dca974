package com.example.demo.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class AuthController {

    @Value("${auth.username}")
    private String configUsername;

    @Value("${auth.password}")
    private String configPassword;

    @PostMapping("/api/auth")
    public ResponseEntity<?> authenticate(@RequestBody AuthRequest request) {
        if (configUsername.equals(request.getUsername()) && 
            configPassword.equals(request.getPassword())) {
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Authentication successful");
            return ResponseEntity.ok(response);
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Invalid credentials");
            return ResponseEntity.status(401).body(response);
        }
    }

    // 内部类用于请求体
    public static class AuthRequest {
        private String username;
        private String password;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}