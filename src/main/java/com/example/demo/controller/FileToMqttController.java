package com.example.demo.controller;

import com.example.demo.service.FileToMqttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/file-to-mqtt")
public class FileToMqttController {

    @Autowired
    private FileToMqttService fileToMqttService;
    @Value("${mqtt.topic}")
    private String topic;
    /**
     * 开始从文件发送数据到MQTT（循环读取模式）
     * 文件路径相对于项目根目录
     * 读完文件后会自动从第一行重新开始读取
     */
    @PostMapping("/start")
    public ResponseEntity<?> startSending(@RequestBody StartRequest request) {
        try {
            // 默认使用项目根目录的result.txt文件
            String filePath = request.getFilePath() != null ? request.getFilePath() : "result.txt";
            long intervalMs = request.getIntervalMs() != null ? request.getIntervalMs() : 50;

            fileToMqttService.startSendingFromFile(filePath, intervalMs, topic);

            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Started sending data from file: " + filePath);
            response.put("intervalMs", intervalMs);
            response.put("topic", topic);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to start sending: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 停止发送服务
     */
    @PostMapping("/stop")
    public ResponseEntity<?> stopSending() {
        fileToMqttService.stop();

        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "File to MQTT service stopped");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<?> getStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("isRunning", fileToMqttService.isRunning());

        return ResponseEntity.ok(response);
    }

    // 请求体类
    public static class StartRequest {
        private String filePath;
        private Long intervalMs;
        private String topic;

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public Long getIntervalMs() {
            return intervalMs;
        }

        public void setIntervalMs(Long intervalMs) {
            this.intervalMs = intervalMs;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }
    }
}
