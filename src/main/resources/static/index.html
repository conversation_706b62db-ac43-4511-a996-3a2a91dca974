<!DOCTYPE html>
<html>
<head>
    <title>MQTT WebSocket Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stomp-websocket@2.3.4-next/lib/stomp.min.js"></script>
    <script>
        let stompClient = null;

        function connect() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            stompClient.connect({}, function(frame) {
                console.log('Connected: ' + frame);
                stompClient.subscribe('/topic/messages', function(message) {
                    showMessage(message.body);
                });
            });
        }

        function showMessage(message) {
            const messageArea = document.getElementById('message-area');
            const newMessage = document.createElement('div');
            newMessage.textContent = message;
            messageArea.appendChild(newMessage);
        }

        window.onload = function() {
            connect();
        };
    </script>
</head>
<body>
    <h1>MQTT Messages</h1>
    <div id="message-area" style="border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: scroll;"></div>
</body>
</html>