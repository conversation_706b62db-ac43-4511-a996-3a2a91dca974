/**
 * MQTT WebSocket 客户端类
 * 用于连接Spring Boot MQTT WebSocket桥接服务
 * 
 * 使用方法:
 * const client = new MqttWebSocketClient('http://localhost:8081/ws');
 * client.connect();
 * client.onMessage = (message) => console.log('收到消息:', message);
 */
class MqttWebSocketClient {
    constructor(serverUrl = 'http://localhost:8081/ws') {
        this.serverUrl = serverUrl;
        this.stompClient = null;
        this.isConnected = false;
        this.messageCount = 0;
        this.connectTime = null;
        
        // 事件回调函数
        this.onMessage = null;          // 收到消息时的回调
        this.onConnect = null;          // 连接成功时的回调
        this.onDisconnect = null;       // 断开连接时的回调
        this.onError = null;            // 发生错误时的回调
        this.onStatusChange = null;     // 状态改变时的回调
        
        // 检查依赖
        this.checkDependencies();
    }

    /**
     * 检查必要的依赖库
     */
    checkDependencies() {
        if (typeof SockJS === 'undefined') {
            console.error('SockJS库未加载，请添加: <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>');
            throw new Error('SockJS库未加载');
        }
        if (typeof Stomp === 'undefined') {
            console.error('Stomp库未加载，请添加: <script src="https://cdn.jsdelivr.net/npm/stomp-websocket@2.3.4-next/lib/stomp.min.js"></script>');
            throw new Error('Stomp库未加载');
        }
    }

    /**
     * 连接到WebSocket服务器
     */
    connect() {
        if (this.isConnected) {
            console.warn('WebSocket已经连接');
            return;
        }

        this.updateStatus('connecting', '正在连接...');
        
        try {
            const socket = new SockJS(this.serverUrl);
            this.stompClient = Stomp.over(socket);
            
            // 设置调试模式（生产环境可以关闭）
            this.stompClient.debug = (str) => {
                console.log('STOMP Debug:', str);
            };
            
            // 连接到服务器
            this.stompClient.connect({}, 
                (frame) => this.onConnectSuccess(frame),
                (error) => this.onConnectError(error)
            );
        } catch (error) {
            this.onConnectError(error);
        }
    }

    /**
     * 连接成功处理
     */
    onConnectSuccess(frame) {
        console.log('WebSocket连接成功:', frame);
        this.isConnected = true;
        this.connectTime = Date.now();
        this.messageCount = 0;
        
        this.updateStatus('connected', '已连接');
        
        // 订阅MQTT消息主题
        this.stompClient.subscribe('/topic/messages', (message) => {
            this.handleMessage(message.body);
        });
        
        // 触发连接成功回调
        if (this.onConnect) {
            this.onConnect(frame);
        }
    }

    /**
     * 连接失败处理
     */
    onConnectError(error) {
        console.error('WebSocket连接失败:', error);
        this.isConnected = false;
        this.updateStatus('disconnected', '连接失败: ' + error);
        
        // 触发错误回调
        if (this.onError) {
            this.onError(error);
        }
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (!this.isConnected || !this.stompClient) {
            console.warn('WebSocket未连接');
            return;
        }

        this.stompClient.disconnect(() => {
            console.log('WebSocket已断开连接');
            this.isConnected = false;
            this.connectTime = null;
            this.updateStatus('disconnected', '已断开连接');
            
            // 触发断开连接回调
            if (this.onDisconnect) {
                this.onDisconnect();
            }
        });
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(messageBody) {
        this.messageCount++;
        
        const messageData = {
            content: messageBody,
            timestamp: new Date(),
            count: this.messageCount
        };
        
        console.log('收到MQTT消息:', messageData);
        
        // 触发消息回调
        if (this.onMessage) {
            this.onMessage(messageData);
        }
    }

    /**
     * 更新状态
     */
    updateStatus(status, message) {
        const statusData = {
            status: status,
            message: message,
            timestamp: new Date()
        };
        
        console.log('状态更新:', statusData);
        
        // 触发状态改变回调
        if (this.onStatusChange) {
            this.onStatusChange(statusData);
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            messageCount: this.messageCount,
            connectTime: this.connectTime,
            connectionDuration: this.connectTime ? Date.now() - this.connectTime : 0
        };
    }

    /**
     * 获取连接时长（秒）
     */
    getConnectionDuration() {
        return this.connectTime ? Math.floor((Date.now() - this.connectTime) / 1000) : 0;
    }
}

// 使用示例和工具函数
const MqttWebSocketUtils = {
    /**
     * 创建一个简单的客户端实例
     */
    createSimpleClient(serverUrl, onMessage) {
        const client = new MqttWebSocketClient(serverUrl);
        
        client.onMessage = onMessage;
        client.onConnect = () => console.log('✅ WebSocket连接成功');
        client.onDisconnect = () => console.log('👋 WebSocket连接断开');
        client.onError = (error) => console.error('❌ WebSocket错误:', error);
        
        return client;
    },

    /**
     * 格式化消息显示
     */
    formatMessage(messageData) {
        const time = messageData.timestamp.toLocaleTimeString() + '.' + 
                    messageData.timestamp.getMilliseconds().toString().padStart(3, '0');
        return `[${time}] #${messageData.count}: ${messageData.content}`;
    },

    /**
     * 解析CSV格式的MQTT消息（如果消息是CSV格式）
     */
    parseCSVMessage(csvString) {
        const values = csvString.split(',');
        return {
            raw: csvString,
            values: values,
            count: values.length
        };
    }
};

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MqttWebSocketClient, MqttWebSocketUtils };
}

// 使用示例：
/*
// 1. 基本使用
const client = new MqttWebSocketClient('http://localhost:8081/ws');

client.onMessage = (messageData) => {
    console.log('收到消息:', messageData.content);
    console.log('消息时间:', messageData.timestamp);
    console.log('消息编号:', messageData.count);
};

client.onConnect = () => {
    console.log('连接成功！开始接收MQTT消息');
};

client.connect();

// 2. 使用工具函数创建简单客户端
const simpleClient = MqttWebSocketUtils.createSimpleClient(
    'http://localhost:8081/ws',
    (messageData) => {
        console.log(MqttWebSocketUtils.formatMessage(messageData));
    }
);

simpleClient.connect();

// 3. 断开连接
// client.disconnect();
*/
