/**
 * 原生WebSocket STOMP客户端
 * 用于连接Spring Boot WebSocket服务，不依赖SockJS和STOMP.js库
 */
class NativeWebSocketStompClient {
    constructor(url = 'ws://localhost:8081/ws/websocket') {
        this.url = url;
        this.ws = null;
        this.isConnected = false;
        this.messageCount = 0;
        this.subscriptions = new Map();
        this.subscriptionId = 0;
        
        // 事件回调
        this.onConnect = null;
        this.onDisconnect = null;
        this.onMessage = null;
        this.onError = null;
        this.onDebug = null;
    }
    
    /**
     * 连接到WebSocket服务器
     */
    connect() {
        if (this.isConnected) {
            this.debug('已经连接，忽略连接请求');
            return;
        }
        
        this.debug(`尝试连接到: ${this.url}`);
        
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = (event) => {
                this.debug('WebSocket连接已建立');
                this.sendStompConnect();
            };
            
            this.ws.onmessage = (event) => {
                this.handleStompFrame(event.data);
            };
            
            this.ws.onclose = (event) => {
                this.isConnected = false;
                this.debug(`连接已关闭 (代码: ${event.code}, 原因: ${event.reason})`);
                if (this.onDisconnect) {
                    this.onDisconnect(event);
                }
            };
            
            this.ws.onerror = (error) => {
                this.debug('WebSocket错误: ' + error);
                if (this.onError) {
                    this.onError(error);
                }
            };
            
        } catch (error) {
            this.debug('创建WebSocket连接失败: ' + error.message);
            if (this.onError) {
                this.onError(error);
            }
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.ws && this.isConnected) {
            this.sendStompDisconnect();
            this.ws.close();
        }
    }
    
    /**
     * 订阅主题
     */
    subscribe(destination, callback) {
        if (!this.isConnected) {
            this.debug('未连接，无法订阅');
            return null;
        }
        
        const id = 'sub-' + (this.subscriptionId++);
        this.subscriptions.set(id, { destination, callback });
        
        const subscribeFrame = this.buildStompFrame('SUBSCRIBE', {
            'id': id,
            'destination': destination
        });
        
        this.ws.send(subscribeFrame);
        this.debug(`订阅主题: ${destination} (ID: ${id})`);
        
        return id;
    }
    
    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId) {
        if (!this.isConnected) {
            this.debug('未连接，无法取消订阅');
            return;
        }
        
        if (this.subscriptions.has(subscriptionId)) {
            this.subscriptions.delete(subscriptionId);
            
            const unsubscribeFrame = this.buildStompFrame('UNSUBSCRIBE', {
                'id': subscriptionId
            });
            
            this.ws.send(unsubscribeFrame);
            this.debug(`取消订阅: ${subscriptionId}`);
        }
    }
    
    /**
     * 发送STOMP CONNECT帧
     */
    sendStompConnect() {
        const connectFrame = this.buildStompFrame('CONNECT', {
            'accept-version': '1.0,1.1,2.0',
            'heart-beat': '10000,10000'
        });
        
        this.ws.send(connectFrame);
        this.debug('发送STOMP CONNECT帧');
    }
    
    /**
     * 发送STOMP DISCONNECT帧
     */
    sendStompDisconnect() {
        const disconnectFrame = this.buildStompFrame('DISCONNECT', {});
        this.ws.send(disconnectFrame);
        this.debug('发送STOMP DISCONNECT帧');
    }
    
    /**
     * 构建STOMP帧
     */
    buildStompFrame(command, headers = {}, body = '') {
        let frame = command + '\n';
        
        // 添加头部
        for (const [key, value] of Object.entries(headers)) {
            frame += `${key}:${value}\n`;
        }
        
        // 空行分隔头部和消息体
        frame += '\n';
        
        // 添加消息体
        frame += body;
        
        // STOMP帧以NULL字符结束
        frame += '\0';
        
        return frame;
    }
    
    /**
     * 解析STOMP帧
     */
    parseStompFrame(data) {
        const lines = data.split('\n');
        const command = lines[0];
        const headers = {};
        let bodyStart = 1;
        
        // 解析头部
        for (let i = 1; i < lines.length; i++) {
            if (lines[i] === '') {
                bodyStart = i + 1;
                break;
            }
            const colonIndex = lines[i].indexOf(':');
            if (colonIndex > 0) {
                const key = lines[i].substring(0, colonIndex);
                const value = lines[i].substring(colonIndex + 1);
                headers[key] = value;
            }
        }
        
        // 解析消息体
        const body = lines.slice(bodyStart).join('\n').replace(/\0$/, '');
        
        return {
            command: command,
            headers: headers,
            body: body
        };
    }
    
    /**
     * 处理STOMP帧
     */
    handleStompFrame(data) {
        this.debug('收到STOMP帧: ' + data.replace(/\0/g, '\\0'));
        
        const frame = this.parseStompFrame(data);
        
        switch (frame.command) {
            case 'CONNECTED':
                this.handleConnected(frame);
                break;
            case 'MESSAGE':
                this.handleMessage(frame);
                break;
            case 'ERROR':
                this.handleError(frame);
                break;
            default:
                this.debug('未知STOMP命令: ' + frame.command);
        }
    }
    
    /**
     * 处理CONNECTED帧
     */
    handleConnected(frame) {
        this.isConnected = true;
        this.debug('STOMP连接成功');
        
        if (this.onConnect) {
            this.onConnect(frame);
        }
    }
    
    /**
     * 处理MESSAGE帧
     */
    handleMessage(frame) {
        this.messageCount++;
        
        const messageData = {
            destination: frame.headers.destination,
            content: frame.body,
            headers: frame.headers,
            timestamp: new Date(),
            count: this.messageCount
        };
        
        this.debug(`收到消息 #${this.messageCount}: ${frame.body}`);
        
        // 查找对应的订阅回调
        const subscriptionId = frame.headers.subscription;
        if (subscriptionId && this.subscriptions.has(subscriptionId)) {
            const subscription = this.subscriptions.get(subscriptionId);
            if (subscription.callback) {
                subscription.callback(messageData);
            }
        }
        
        // 全局消息回调
        if (this.onMessage) {
            this.onMessage(messageData);
        }
    }
    
    /**
     * 处理ERROR帧
     */
    handleError(frame) {
        const errorData = {
            message: frame.body,
            headers: frame.headers
        };
        
        this.debug('STOMP错误: ' + frame.body);
        
        if (this.onError) {
            this.onError(errorData);
        }
    }
    
    /**
     * 调试输出
     */
    debug(message) {
        console.log('[NativeWebSocketStompClient]', message);
        if (this.onDebug) {
            this.onDebug(message);
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            messageCount: this.messageCount,
            subscriptionCount: this.subscriptions.size,
            url: this.url
        };
    }
}

// 使用示例
/*
// 创建客户端
const client = new NativeWebSocketStompClient('ws://localhost:8081/ws/websocket');

// 设置事件回调
client.onConnect = () => {
    console.log('✅ 连接成功！');
    
    // 订阅MQTT消息主题
    client.subscribe('/topic/messages', (messageData) => {
        console.log('收到MQTT消息:', messageData.content);
        console.log('消息时间:', messageData.timestamp);
        console.log('消息编号:', messageData.count);
    });
};

client.onDisconnect = () => {
    console.log('👋 连接断开');
};

client.onError = (error) => {
    console.error('❌ 错误:', error);
};

client.onDebug = (message) => {
    console.log('🔧 调试:', message);
};

// 连接
client.connect();

// 断开连接
// client.disconnect();
*/

// 导出模块（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NativeWebSocketStompClient;
}
