/**
 * 带重连机制的WebSocket STOMP客户端
 * 支持自动重连、心跳检测、连接状态管理
 */
class ReconnectingWebSocketClient {
    constructor(options = {}) {
        // 配置参数
        this.url = options.url || 'ws://localhost:8081/ws/websocket';
        this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
        this.reconnectInterval = options.reconnectInterval || 3000; // 3秒
        this.maxReconnectInterval = options.maxReconnectInterval || 30000; // 30秒
        this.reconnectDecay = options.reconnectDecay || 1.5; // 重连间隔递增倍数
        this.heartbeatInterval = options.heartbeatInterval || 25000; // 25秒心跳
        this.debug = options.debug || false;
        
        // 状态管理
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.shouldReconnect = true;
        this.reconnectAttempts = 0;
        this.currentReconnectInterval = this.reconnectInterval;
        this.messageCount = 0;
        this.subscriptions = new Map();
        this.subscriptionId = 0;
        
        // 定时器
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.connectionTimeoutTimer = null;
        
        // 事件回调
        this.onConnect = null;
        this.onDisconnect = null;
        this.onMessage = null;
        this.onError = null;
        this.onReconnecting = null;
        this.onReconnectFailed = null;
        this.onStatusChange = null;
        this.onDebug = null;
    }
    
    /**
     * 连接到WebSocket服务器
     */
    connect() {
        if (this.isConnecting || this.isConnected) {
            this.log('已经在连接中或已连接，忽略连接请求');
            return;
        }
        
        this.isConnecting = true;
        this.shouldReconnect = true;
        this.updateStatus('connecting', '正在连接...');
        this.log(`尝试连接到: ${this.url}`);
        
        try {
            this.ws = new WebSocket(this.url);
            
            // 设置连接超时
            this.connectionTimeoutTimer = setTimeout(() => {
                if (this.isConnecting) {
                    this.log('连接超时');
                    this.ws.close();
                    this.handleConnectionFailure('连接超时');
                }
            }, 10000); // 10秒超时
            
            this.ws.onopen = (event) => {
                this.clearConnectionTimeout();
                this.isConnecting = false;
                this.log('WebSocket连接已建立');
                this.sendStompConnect();
            };
            
            this.ws.onmessage = (event) => {
                this.handleStompFrame(event.data);
            };
            
            this.ws.onclose = (event) => {
                this.clearConnectionTimeout();
                this.handleDisconnection(event);
            };
            
            this.ws.onerror = (error) => {
                this.clearConnectionTimeout();
                this.log('WebSocket错误: ' + error);
                if (this.onError) {
                    this.onError(error);
                }
            };
            
        } catch (error) {
            this.clearConnectionTimeout();
            this.isConnecting = false;
            this.log('创建WebSocket连接失败: ' + error.message);
            this.handleConnectionFailure(error.message);
        }
    }
    
    /**
     * 手动断开连接
     */
    disconnect() {
        this.shouldReconnect = false;
        this.clearReconnectTimer();
        this.clearHeartbeatTimer();
        this.clearConnectionTimeout();
        
        if (this.ws && this.isConnected) {
            this.sendStompDisconnect();
            this.ws.close(1000, '手动断开连接');
        } else if (this.ws) {
            this.ws.close();
        }
        
        this.updateStatus('disconnected', '已断开连接');
    }
    
    /**
     * 处理连接失败
     */
    handleConnectionFailure(reason) {
        this.isConnecting = false;
        this.isConnected = false;
        this.updateStatus('disconnected', `连接失败: ${reason}`);
        
        if (this.shouldReconnect) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 处理断开连接
     */
    handleDisconnection(event) {
        const wasConnected = this.isConnected;
        this.isConnecting = false;
        this.isConnected = false;
        this.clearHeartbeatTimer();
        this.subscriptions.clear();
        
        this.log(`连接已关闭 (代码: ${event.code}, 原因: ${event.reason || '未知'})`);
        this.updateStatus('disconnected', `连接已关闭 (${event.code})`);
        
        if (wasConnected && this.onDisconnect) {
            this.onDisconnect(event);
        }
        
        // 如果不是手动断开且需要重连
        if (this.shouldReconnect && event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.log(`达到最大重连次数 (${this.maxReconnectAttempts})，停止重连`);
            this.updateStatus('failed', '重连失败，已达到最大尝试次数');
            if (this.onReconnectFailed) {
                this.onReconnectFailed();
            }
            return;
        }
        
        this.reconnectAttempts++;
        this.log(`安排第 ${this.reconnectAttempts} 次重连，${this.currentReconnectInterval}ms 后执行`);
        
        this.updateStatus('reconnecting', `重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        if (this.onReconnecting) {
            this.onReconnecting(this.reconnectAttempts, this.maxReconnectAttempts);
        }
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.currentReconnectInterval);
        
        // 增加重连间隔（指数退避）
        this.currentReconnectInterval = Math.min(
            this.currentReconnectInterval * this.reconnectDecay,
            this.maxReconnectInterval
        );
    }
    
    /**
     * 重置重连状态
     */
    resetReconnectState() {
        this.reconnectAttempts = 0;
        this.currentReconnectInterval = this.reconnectInterval;
        this.clearReconnectTimer();
    }
    
    /**
     * 发送STOMP CONNECT帧
     */
    sendStompConnect() {
        const connectFrame = this.buildStompFrame('CONNECT', {
            'accept-version': '1.0,1.1,2.0',
            'heart-beat': `${this.heartbeatInterval},${this.heartbeatInterval}`
        });
        
        this.ws.send(connectFrame);
        this.log('发送STOMP CONNECT帧');
    }
    
    /**
     * 发送STOMP DISCONNECT帧
     */
    sendStompDisconnect() {
        const disconnectFrame = this.buildStompFrame('DISCONNECT', {});
        this.ws.send(disconnectFrame);
        this.log('发送STOMP DISCONNECT帧');
    }
    
    /**
     * 订阅主题
     */
    subscribe(destination, callback) {
        if (!this.isConnected) {
            this.log('未连接，无法订阅');
            return null;
        }
        
        const id = 'sub-' + (this.subscriptionId++);
        this.subscriptions.set(id, { destination, callback });
        
        const subscribeFrame = this.buildStompFrame('SUBSCRIBE', {
            'id': id,
            'destination': destination
        });
        
        this.ws.send(subscribeFrame);
        this.log(`订阅主题: ${destination} (ID: ${id})`);
        
        return id;
    }
    
    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId) {
        if (!this.isConnected) {
            this.log('未连接，无法取消订阅');
            return;
        }
        
        if (this.subscriptions.has(subscriptionId)) {
            this.subscriptions.delete(subscriptionId);
            
            const unsubscribeFrame = this.buildStompFrame('UNSUBSCRIBE', {
                'id': subscriptionId
            });
            
            this.ws.send(unsubscribeFrame);
            this.log(`取消订阅: ${subscriptionId}`);
        }
    }
    
    /**
     * 构建STOMP帧
     */
    buildStompFrame(command, headers = {}, body = '') {
        let frame = command + '\n';
        
        for (const [key, value] of Object.entries(headers)) {
            frame += `${key}:${value}\n`;
        }
        
        frame += '\n' + body + '\0';
        return frame;
    }
    
    /**
     * 解析STOMP帧
     */
    parseStompFrame(data) {
        const lines = data.split('\n');
        const command = lines[0];
        const headers = {};
        let bodyStart = 1;
        
        for (let i = 1; i < lines.length; i++) {
            if (lines[i] === '') {
                bodyStart = i + 1;
                break;
            }
            const colonIndex = lines[i].indexOf(':');
            if (colonIndex > 0) {
                const key = lines[i].substring(0, colonIndex);
                const value = lines[i].substring(colonIndex + 1);
                headers[key] = value;
            }
        }
        
        const body = lines.slice(bodyStart).join('\n').replace(/\0$/, '');
        
        return { command, headers, body };
    }
    
    /**
     * 处理STOMP帧
     */
    handleStompFrame(data) {
        this.log('收到STOMP帧: ' + data.replace(/\0/g, '\\0'));
        
        const frame = this.parseStompFrame(data);
        
        switch (frame.command) {
            case 'CONNECTED':
                this.handleConnected(frame);
                break;
            case 'MESSAGE':
                this.handleMessage(frame);
                break;
            case 'ERROR':
                this.handleError(frame);
                break;
            default:
                this.log('未知STOMP命令: ' + frame.command);
        }
    }
    
    /**
     * 处理CONNECTED帧
     */
    handleConnected(frame) {
        this.isConnected = true;
        this.resetReconnectState();
        this.startHeartbeat();
        
        this.log('STOMP连接成功');
        this.updateStatus('connected', '已连接');
        
        if (this.onConnect) {
            this.onConnect(frame);
        }
    }
    
    /**
     * 处理MESSAGE帧
     */
    handleMessage(frame) {
        this.messageCount++;
        
        const messageData = {
            destination: frame.headers.destination,
            content: frame.body,
            headers: frame.headers,
            timestamp: new Date(),
            count: this.messageCount
        };
        
        this.log(`收到消息 #${this.messageCount}: ${frame.body}`);
        
        // 订阅回调
        const subscriptionId = frame.headers.subscription;
        if (subscriptionId && this.subscriptions.has(subscriptionId)) {
            const subscription = this.subscriptions.get(subscriptionId);
            if (subscription.callback) {
                subscription.callback(messageData);
            }
        }
        
        // 全局消息回调
        if (this.onMessage) {
            this.onMessage(messageData);
        }
    }
    
    /**
     * 处理ERROR帧
     */
    handleError(frame) {
        const errorData = {
            message: frame.body,
            headers: frame.headers
        };
        
        this.log('STOMP错误: ' + frame.body);
        
        if (this.onError) {
            this.onError(errorData);
        }
    }
    
    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.clearHeartbeatTimer();
        
        if (this.heartbeatInterval > 0) {
            this.heartbeatTimer = setInterval(() => {
                if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send('\n'); // 发送心跳帧
                    this.log('发送心跳');
                }
            }, this.heartbeatInterval);
        }
    }
    
    /**
     * 清除定时器
     */
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    
    clearHeartbeatTimer() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    clearConnectionTimeout() {
        if (this.connectionTimeoutTimer) {
            clearTimeout(this.connectionTimeoutTimer);
            this.connectionTimeoutTimer = null;
        }
    }
    
    /**
     * 更新状态
     */
    updateStatus(status, message) {
        const statusData = {
            status: status,
            message: message,
            timestamp: new Date(),
            reconnectAttempts: this.reconnectAttempts,
            messageCount: this.messageCount
        };
        
        this.log(`状态更新: ${status} - ${message}`);
        
        if (this.onStatusChange) {
            this.onStatusChange(statusData);
        }
    }
    
    /**
     * 日志输出
     */
    log(message) {
        if (this.debug) {
            console.log('[ReconnectingWebSocketClient]', message);
        }
        if (this.onDebug) {
            this.onDebug(message);
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            reconnectAttempts: this.reconnectAttempts,
            messageCount: this.messageCount,
            subscriptionCount: this.subscriptions.size,
            url: this.url
        };
    }
    
    /**
     * 销毁客户端
     */
    destroy() {
        this.shouldReconnect = false;
        this.disconnect();
        this.clearReconnectTimer();
        this.clearHeartbeatTimer();
        this.clearConnectionTimeout();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReconnectingWebSocketClient;
}
